use std::cell::RefCell;
use std::io;
use std::net::{IpAddr, Ipv4Addr, SocketAddr, ToSocketAddrs};
use std::rc::Rc;
use std::str::FromStr;
use std::time::{Duration, SystemTime, UNIX_EPOCH};

use mio::Token;

use crate::encoding::sbe::{parse_sbe_bookticker, parse_sbe_depth_snapshot, parse_sbe_trades};
use crate::engine::binance::{
    generate_market_data_ws_url_for_perf, generate_sbe_depth_20_url, generate_sbe_trade_url,
};
use crate::engine::trade::generate_user_data_sub_request;
use crate::engine::trading_pair::API_KEY;
use crate::utils::perf::circles_to_ns;
use crate::{
    CallbackData, Message, Result, Settings, WebSocket, WebSocketHandle,
    encoding::order_update::{OrderResponse, parse_order_update},
    engine::trade::{generate_order_request_by_symbol, generate_session_logon_request},
    net::utils::url::Url,
    utils,
};
use crate::{flush_logs, logln};

const MARKET_DATA_TOKEN: Token = Token(0);
const ORDER_TOKEN_1: Token = Token(1);
const ORDER_TOKEN_2: Token = Token(2);
const ORDER_TOKEN_3: Token = Token(3);
const ORDER_TOKEN_4: Token = Token(4);
const TRADE_TOKEN: Token = Token(5);
const DEPTH_SS_TOKEN_1: Token = Token(6);

/// 解析域名获取所有IP地址
///
/// # Arguments
/// * `domain` - 要解析的域名
/// * `port` - 端口号（可选，默认为443）
///
/// # Returns
/// * `Result<Vec<SocketAddr>, io::Error>` - 成功时返回所有解析到的IP地址，失败时返回错误
pub fn resolve_domain_ips(domain: &str, port: Option<u16>) -> Result<Vec<SocketAddr>> {
    let port = port.unwrap_or(443);
    let address = format!("{}:{}", domain, port);

    match address.to_socket_addrs() {
        Ok(addrs) => {
            let ip_list: Vec<SocketAddr> = addrs.collect();
            if ip_list.is_empty() {
                Err(io::Error::new(
                    io::ErrorKind::NotFound,
                    format!("No IP addresses found for domain: {}", domain),
                )
                .into())
            } else {
                Ok(ip_list)
            }
        }
        Err(e) => Err(e.into()),
    }
}

/// 多次解析域名以获取更完整的IP列表
///
/// # Arguments
/// * `domain` - 要解析的域名
/// * `port` - 端口号（可选，默认为443）
/// * `attempts` - 尝试次数（默认为5次）
/// * `delay_ms` - 每次尝试之间的延迟（毫秒，默认为100ms）
///
/// # Returns
/// * `Result<Vec<SocketAddr>, io::Error>` - 成功时返回去重后的所有IP地址
pub fn resolve_domain_ips_multiple(
    domain: &str,
    port: Option<u16>,
    attempts: Option<usize>,
    delay_ms: Option<u64>,
) -> Result<Vec<SocketAddr>> {
    let attempts = attempts.unwrap_or(5);
    let delay = Duration::from_millis(delay_ms.unwrap_or(100));
    let mut all_ips = Vec::new();
    let mut last_error = None;

    for i in 0..attempts {
        match resolve_domain_ips(domain, port) {
            Ok(ips) => {
                for ip in ips {
                    if !all_ips.contains(&ip) {
                        all_ips.push(ip);
                    }
                }
            }
            Err(e) => {
                last_error = Some(e);
                logln!(
                    "Attempt {} failed to resolve {}: {}",
                    i + 1,
                    domain,
                    last_error.as_ref().unwrap()
                );
            }
        }

        // 如果不是最后一次尝试，则等待
        if i < attempts - 1 {
            std::thread::sleep(delay);
        }
    }

    if all_ips.is_empty() {
        Err(last_error.unwrap_or_else(|| {
            io::Error::new(
                io::ErrorKind::NotFound,
                format!("Failed to resolve any IP addresses for domain: {}", domain),
            )
            .into()
        }))
    } else {
        Ok(all_ips)
    }
}

#[derive(Debug)]
struct LatencyStats {
    market_data_latencies: [f64; 10000],
    trade_data_latencies: [f64; 10000],
    depth_data_latencies: [f64; 10000],
    order_latencies: [f64; 1000],
    market_data_index: usize,
    trade_data_index: usize,
    depth_data_index: usize,
    order_index: usize,
    market_data_ip: String,
    order_ip: String,
}

impl LatencyStats {
    fn new(market_data_ip: String, order_ip: String) -> Self {
        Self {
            market_data_latencies: [0.0; 10000],
            trade_data_latencies: [0.0; 10000],
            depth_data_latencies: [0.0; 10000],
            order_latencies: [0.0; 1000],
            market_data_ip,
            order_ip,
            market_data_index: 0,
            trade_data_index: 0,
            depth_data_index: 0,
            order_index: 0,
        }
    }

    fn add_market_data_latency(&mut self, latency_ns: f64) {
        if self.market_data_index >= 10000 {
            return;
        }
        self.market_data_latencies[self.market_data_index] = latency_ns;
        self.market_data_index += 1;
    }

    fn add_order_latency(&mut self, latency_ns: f64) {
        if self.order_index >= 1000 {
            return;
        }
        self.order_latencies[self.order_index] = latency_ns;
        self.order_index += 1;
    }

    fn add_trade_latency(&mut self, latency_ns: f64) {
        if self.trade_data_index >= 10000 {
            return;
        }
        self.trade_data_latencies[self.trade_data_index] = latency_ns;
        self.trade_data_index += 1;
    }

    fn add_depth_latency(&mut self, latency_ns: f64) {
        if self.depth_data_index >= 10000 {
            return;
        }
        self.depth_data_latencies[self.depth_data_index] = latency_ns;
        self.depth_data_index += 1;
    }

    fn is_full(&self) -> bool {
        self.order_index >= 200
    }

    fn print_and_return_stats(&self) -> (f64, f64, f64, f64) {
        logln!("== Endpoint Performance Report ===");
        logln!("Market Data IP: {}", self.market_data_ip);
        logln!("Order API IP: {}", self.order_ip);

        let mut md_latencies = self.market_data_latencies.clone();
        let md_p50 = utils::perf::percentile(&mut md_latencies[..self.market_data_index], 0.50);

        logln!("Market Data Latency: p50: {:.2}us", md_p50);

        let mut trade_latencies = self.trade_data_latencies.clone();
        let trade_p50 =
            utils::perf::percentile(&mut trade_latencies[..self.trade_data_index], 0.50);
        logln!("Trade Data Latency: p50: {:.2}us", trade_p50);

        let mut depth_latencies = self.depth_data_latencies.clone();
        let depth_p50 =
            utils::perf::percentile(&mut depth_latencies[..self.depth_data_index], 0.50);
        logln!("Depth Data Latency: p50: {:.2}us", depth_p50);

        let mut order_latencies = self.order_latencies.clone();
        let p50 = utils::perf::percentile(&mut order_latencies[..self.order_index], 0.50);
        logln!("Order Response Latency: p50: {:.2}us", p50 / 1000.0);
        logln!("=====================================\n");
        flush_logs!();
        (md_p50, trade_p50, depth_p50, p50)
    }
}

fn generate_order_url() -> String {
    "wss://ws-api.binance.com:443/ws-api/v3".to_string()
}

pub fn test_ip_latency(market_data_ip: &str, order_ip: &str) -> Option<(f64, f64, f64, f64)> {
    const IN_LEN: usize = 1024 * 8;
    const OUT_LEN: usize = 1024 * 2;
    let mut user_data = false;
    let mut last_order_place_time: u64 = utils::perf::now();
    let mut stats = LatencyStats::new(market_data_ip.to_string(), order_ip.to_string());

    // 用于存储统计结果的共享变量
    let result = Rc::new(RefCell::new(None::<(f64, f64, f64, f64)>));
    let result_clone = Rc::clone(&result);
    let callback = move |handle: &mut WebSocketHandle<IN_LEN, OUT_LEN>,
                         cd: CallbackData|
          -> Result<()> {
        match cd {
            CallbackData::Message(token, msg) => {
                match msg {
                    Message::WebsocketPayload(data) => match token {
                        MARKET_DATA_TOKEN => {
                            if let Some(book_ticker) = parse_sbe_bookticker(data.as_ref()) {
                                let current_time_us = SystemTime::now()
                                    .duration_since(UNIX_EPOCH)
                                    .unwrap()
                                    .as_micros()
                                    as u64;
                                let market_data_latency_us =
                                    current_time_us as f64 - book_ticker.event_time as f64;
                                stats.add_market_data_latency(market_data_latency_us);

                                let bid = book_ticker.bid_price;
                                let now = utils::perf::now();
                                if !user_data
                                    || circles_to_ns(now - last_order_place_time) < 400_000_000.0
                                {
                                    return Ok(());
                                }
                                let buf = handle.get_write_buf(ORDER_TOKEN_1)?;
                                generate_order_request_by_symbol(now, buf, bid, book_ticker.symbol);
                                logln!("posting order for symbol: {}", book_ticker.symbol);
                                let buf = handle.get_write_buf(ORDER_TOKEN_2)?;
                                generate_order_request_by_symbol(now, buf, bid, book_ticker.symbol);
                                let buf = handle.get_write_buf(ORDER_TOKEN_3)?;
                                generate_order_request_by_symbol(now, buf, bid, book_ticker.symbol);
                                let buf = handle.get_write_buf(ORDER_TOKEN_4)?;
                                generate_order_request_by_symbol(now, buf, bid, book_ticker.symbol);
                                handle.trigger_write(ORDER_TOKEN_1)?;
                                handle.trigger_write(ORDER_TOKEN_2)?;
                                handle.trigger_write(ORDER_TOKEN_3)?;
                                handle.trigger_write(ORDER_TOKEN_4)?;
                                last_order_place_time = now;
                            } else {
                                logln!(
                                    "Failed to parse market data, {:?}",
                                    String::from_utf8_lossy(data.as_ref())
                                );
                            }
                        }
                        TRADE_TOKEN => {
                            if let Some(trade) = parse_sbe_trades(data.as_ref()) {
                                let current_time_us = SystemTime::now()
                                    .duration_since(UNIX_EPOCH)
                                    .unwrap()
                                    .as_micros()
                                    as u64;
                                let trade_latency_us =
                                    current_time_us as f64 - trade.event_time as f64;
                                stats.add_trade_latency(trade_latency_us);
                            } else {
                                logln!(
                                    "Failed to parse trade data, {:?}",
                                    String::from_utf8_lossy(data.as_ref())
                                );
                            }
                        }
                        DEPTH_SS_TOKEN_1 => {
                            if let Some(depth_snapshot) = parse_sbe_depth_snapshot(data.as_ref()) {
                                let current_time_us = SystemTime::now()
                                    .duration_since(UNIX_EPOCH)
                                    .unwrap()
                                    .as_micros()
                                    as u64;
                                let depth_latency_us =
                                    current_time_us as f64 - depth_snapshot.event_time as f64;
                                stats.add_depth_latency(depth_latency_us);
                            } else {
                                logln!(
                                    "Failed to parse depth snapshot, {:?}",
                                    String::from_utf8_lossy(data.as_ref())
                                );
                            }
                        }
                        ORDER_TOKEN_1 | ORDER_TOKEN_2 | ORDER_TOKEN_3 | ORDER_TOKEN_4 => {
                            if !user_data {
                                handle
                                    .send_message(token, generate_user_data_sub_request())
                                    .unwrap();
                                user_data = true;
                            }
                            let now = utils::perf::now();
                            if user_data {
                                if let Some(OrderResponse::OrderUpdate(ou)) =
                                    parse_order_update(data.as_ref())
                                {
                                    if ou.status == "NEW" {
                                        let circles = now - ou.order_id.order_create_time;
                                        stats
                                            .add_order_latency(utils::perf::circles_to_ns(circles));

                                        if stats.is_full() {
                                            let latency_stats = stats.print_and_return_stats();
                                            // 将结果存储到共享变量中
                                            *result_clone.borrow_mut() = Some(latency_stats);
                                            handle.stop();
                                        }
                                    }
                                } else {
                                    if memchr::memmem::find(
                                        data.as_ref(),
                                        b"outboundAccountPosition",
                                    )
                                    .is_some()
                                        || memchr::memmem::find(data.as_ref(), b"clientOrderId")
                                            .is_some()
                                        || memchr::memmem::find(data.as_ref(), b"authorizedSince")
                                            .is_some()
                                        || memchr::memmem::find(data.as_ref(), b"200").is_some()
                                    {
                                        return Ok(());
                                    }
                                    logln!(
                                        "Failed to parse order update, {:?}",
                                        String::from_utf8_lossy(data.as_ref())
                                    );
                                }
                            }
                        }
                        _ => (),
                    },
                    _ => {
                        logln!("recv other msg: {:?}", msg);
                    }
                }
            }
            CallbackData::ConnectionOpen(token) => match token {
                ORDER_TOKEN_1 | ORDER_TOKEN_2 | ORDER_TOKEN_3 | ORDER_TOKEN_4 => {
                    handle.send_message(token, generate_session_logon_request())?;
                }
                _ => {}
            },
            _ => {
                logln!("got other callback: {:?}", cd);
                handle.stop();
            }
        }
        Ok(())
    };
    let settings = Settings::default();
    let mut websocket = WebSocket::new(settings, callback).unwrap();

    let mut headers = std::collections::HashMap::new();
    headers.insert("X-MBX-APIKEY".to_string(), API_KEY.to_string());

    let mut bbo_url: Url = generate_market_data_ws_url_for_perf().into();
    bbo_url.socket_addr = Some(SocketAddr::new(
        IpAddr::V4(Ipv4Addr::from_str(market_data_ip).unwrap()),
        bbo_url.port,
    ));

    websocket
        .connect_with_headers(bbo_url, MARKET_DATA_TOKEN, headers.clone())
        .unwrap();

    let mut trade_url: Url = generate_sbe_trade_url().into();
    trade_url.socket_addr = Some(SocketAddr::new(
        IpAddr::V4(Ipv4Addr::from_str(market_data_ip).unwrap()),
        trade_url.port,
    ));
    websocket
        .connect_with_headers(trade_url, TRADE_TOKEN, headers.clone())
        .unwrap();

    let mut depth_ss_url: Url = generate_sbe_depth_20_url(1).into();
    depth_ss_url.socket_addr = Some(SocketAddr::new(
        IpAddr::V4(Ipv4Addr::from_str(market_data_ip).unwrap()),
        depth_ss_url.port,
    ));
    websocket
        .connect_with_headers(depth_ss_url, DEPTH_SS_TOKEN_1, headers.clone())
        .unwrap();

    let mut order_url: Url = generate_order_url().into();
    order_url.socket_addr = Some(SocketAddr::new(
        IpAddr::V4(Ipv4Addr::from_str(order_ip).unwrap()),
        443,
    ));
    websocket.connect(order_url.clone(), ORDER_TOKEN_1).unwrap();
    websocket.connect(order_url.clone(), ORDER_TOKEN_2).unwrap();
    websocket.connect(order_url.clone(), ORDER_TOKEN_3).unwrap();
    websocket.connect(order_url.clone(), ORDER_TOKEN_4).unwrap();
    match websocket.run() {
        Ok(_) => (),
        Err(e) => {
            logln!("websocket run error: {:?}", e);
        }
    }

    // 返回存储的统计结果
    *result.borrow()
}

pub fn measure() -> (
    Option<String>,
    Option<String>,
    Option<String>,
    Option<String>,
) {
    let stream_sbe_ips =
        resolve_domain_ips_multiple("stream-sbe.binance.com", Some(443), Some(5), Some(200))
            .unwrap();
    logln!(
        "stream-sbe.binance.com resolved to {} IPs",
        stream_sbe_ips.len()
    );
    let ws_api_ips =
        resolve_domain_ips_multiple("ws-api.binance.com", Some(443), Some(5), Some(200)).unwrap();
    logln!("ws-api.binance.com resolved to {} IPs", ws_api_ips.len());

    let mut results = Vec::new();

    for i in 0..stream_sbe_ips.len() {
        let market_data_ip = stream_sbe_ips[i];
        let ws_api_ip = ws_api_ips[i % ws_api_ips.len()];
        let market_data_ip_str = market_data_ip.ip().to_string();
        let ws_api_ip_str = ws_api_ip.ip().to_string();

        if let Some((md_p50, trade_p50, depth_p50, order_p50)) =
            test_ip_latency(&market_data_ip_str, &ws_api_ip_str)
        {
            results.push((
                market_data_ip_str,
                ws_api_ip_str,
                md_p50,
                trade_p50,
                depth_p50,
                order_p50,
            ));
        }
    }

    let mut ret = (None, None, None, None);
    if !results.is_empty() {
        logln!("=== All tests results ===");
        for (md_ip, order_ip, md_p50, trade_p50, depth_p50, order_p50) in &results {
            logln!(
                "Market Data IP: {}, Order IP: {}, MD p50: {:.2}us, Trade p50: {:.2}us, Depth p50: {:.2}us, Order p50: {:.2}us",
                md_ip,
                order_ip,
                md_p50,
                trade_p50,
                depth_p50,
                order_p50 / 1000.0
            );
        }

        // 找到最佳Market Data IP
        if let Some((best_md_ip, _, best_md_p50, _, _, _)) =
            results.iter().min_by(|a, b| a.2.partial_cmp(&b.2).unwrap())
        {
            logln!("=== Best Market Data IP ===");
            logln!("IP: {}, p50: {:.2}us", best_md_ip, best_md_p50);
            ret.0 = Some(best_md_ip.clone());
        }

        if let Some((best_md_ip, _, _, best_trade_p50, _, _)) =
            results.iter().min_by(|a, b| a.3.partial_cmp(&b.3).unwrap())
        {
            logln!("=== Best Trade Data IP ===");
            logln!("IP: {}, p50: {:.2}us", best_md_ip, best_trade_p50);
            ret.1 = Some(best_md_ip.clone());
        }

        if let Some((best_depth_ip, _, _, _, best_depth_p50, _)) =
            results.iter().min_by(|a, b| a.4.partial_cmp(&b.4).unwrap())
        {
            logln!("=== Best Depth Data IP ===");
            logln!("IP: {}, p50: {:.2}us", best_depth_ip, best_depth_p50);
            ret.2 = Some(best_depth_ip.clone());
        }

        if let Some((_, best_order_ip, _, _, _, best_order_p50)) =
            results.iter().min_by(|a, b| a.5.partial_cmp(&b.5).unwrap())
        {
            logln!("=== Best Order IP ===");
            logln!(
                "IP: {}, p50: {:.2}us",
                best_order_ip,
                best_order_p50 / 1000.0
            );
            ret.3 = Some(best_order_ip.clone());
        }
        flush_logs!();
    } else {
        logln!("no success results");
    }

    ret
}
