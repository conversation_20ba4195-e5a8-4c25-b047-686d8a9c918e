use crate::{
    Currency, EdgeDirection, ORDER_FILTER_LOT_SIZE_INDEX, ORDER_FILTER_MIN_NOTIONAL_INDEX,
    ORDER_FILTER_MIN_ORDER_QTY_INDEX, ORDER_FILTERS, ORDER_QUANTITIES, PREDEFINED_RINGS,
    TRADING_PAIR_RATES, TRADING_PAIR_TO_RING_INDEX, TradingPair, WebSocketHandle,
    encoding::{
        agg_trades::AggTrade,
        book_ticker::BookTicker,
        depth_snapshot::DepthSnapshot,
        depth_update::DepthUpdate,
        sbe::{SbeBookTicker, SbeDepthSnapshot, SbeDepthUpdate, SbeTrade},
    },
    engine::{
        monitor::CURRENT_RING_TS,
        token::{ORDER_TOKEN_1, ORDER_TOKEN_2, ORDER_TOKEN_3, ORDER_TOKEN_4},
        trade::generate_order_requests,
        trading_pair::{
            ORDER_BOOKS, TRADING_PAIR_RATE_EVENT_TIME, TRADING_PAIR_RATE_FROM,
            TRADING_PAIR_RATE_UPDATE_ID, TRADING_PAIR_RATE_UPDATE_TIME,
        },
    },
    flush_logs, logln,
    utils::{
        self,
        perf::{circles_to_ns, system_now_in_us},
    },
};

// BNB 折扣率 (75% 折扣，即支付原价的 75%)
// const BNB_DISCOUNT: f64 = 0.75;

fn adjust_quantity(
    base_q: f64,
    quote_q: f64,
    min_qty: f64,
    step: f64,
    rate: f64,
    min_national: f64,
) -> Option<f64> {
    // logln!(
    //     "base_q: {}, quote_q: {}, min_qty: {}, step: {}, rate: {} min_national: {}",
    //     base_q,
    //     quote_q,
    //     min_qty,
    //     step,
    //     rate,
    //     min_national
    // );
    if base_q < min_qty {
        return None;
    }
    if quote_q < min_national {
        return None;
    }
    let steps = (base_q / step).floor();
    let adjusted = steps * step;
    if adjusted > min_qty {
        return Some(adjusted);
    }
    return Some(min_qty);
}

fn quote_to_base_quantity(quote: f64, pair: TradingPair) -> f64 {
    unsafe { quote * TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] }
}

fn base_to_quote_quantity(base: f64, pair: TradingPair) -> f64 {
    unsafe { base * TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] }
}

pub static mut USER_DATA_SUBSCRIBED: bool = false;
pub static mut TRADING_FEE_UPDATED: bool = true;

pub struct ArbitrageEngine {}

impl ArbitrageEngine {
    pub fn set_user_data_subscribed() {
        unsafe {
            USER_DATA_SUBSCRIBED = true;
        }
    }

    pub fn unset_user_data_subscribed() {
        unsafe {
            USER_DATA_SUBSCRIBED = false;
        }
    }

    pub fn user_data_subscribed() -> bool {
        unsafe { USER_DATA_SUBSCRIBED }
    }

    pub fn set_trading_fee_updated() {
        unsafe {
            TRADING_FEE_UPDATED = true;
        }
    }

    pub fn get_trading_fee_updated() -> bool {
        unsafe { TRADING_FEE_UPDATED }
    }

    #[inline(always)]
    pub fn check_arbitrage_conditions(now: u64) -> bool {
        unsafe {
            USER_DATA_SUBSCRIBED && TRADING_FEE_UPDATED && {
                circles_to_ns(now - CURRENT_RING_TS) > 200_000_000.0
            }
        }
    }

    // #[perf_macro::measure]
    #[inline(always)]
    pub fn place_orders<const IN_LEN: usize, const OUT_LEN: usize>(
        ring_index: usize,
        now: u64,
        handle: &mut WebSocketHandle<IN_LEN, OUT_LEN>,
    ) {
        let ring = PREDEFINED_RINGS[ring_index];
        let len = ring.len();

        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        match len {
            3 => {
                let buf = handle.get_write_buf(ORDER_TOKEN_1).unwrap();
                generate_order_requests(ring, ring_index, now, timestamp, buf, 0);
                let buf = handle.get_write_buf(ORDER_TOKEN_2).unwrap();
                generate_order_requests(ring, ring_index, now, timestamp, buf, 1);
                let buf = handle.get_write_buf(ORDER_TOKEN_3).unwrap();
                generate_order_requests(ring, ring_index, now, timestamp, buf, 2);
                handle
                    .trigger_write_3(ORDER_TOKEN_1, ORDER_TOKEN_2, ORDER_TOKEN_3)
                    .unwrap();
            }
            4 => {
                let buf = handle.get_write_buf(ORDER_TOKEN_1).unwrap();
                generate_order_requests(ring, ring_index, now, timestamp, buf, 0);
                let buf = handle.get_write_buf(ORDER_TOKEN_2).unwrap();
                generate_order_requests(ring, ring_index, now, timestamp, buf, 1);
                let buf = handle.get_write_buf(ORDER_TOKEN_3).unwrap();
                generate_order_requests(ring, ring_index, now, timestamp, buf, 2);
                let buf = handle.get_write_buf(ORDER_TOKEN_4).unwrap();
                generate_order_requests(ring, ring_index, now, timestamp, buf, 3);

                handle
                    .trigger_write_4(ORDER_TOKEN_1, ORDER_TOKEN_2, ORDER_TOKEN_3, ORDER_TOKEN_4)
                    .unwrap();
            }
            _ => {}
        }
        let end = utils::perf::now();
        let mut buf = ryu::Buffer::new();
        let latency_str = buf.format(circles_to_ns(end - now));
        logln!("Total latency: {}ns", latency_str);
        unsafe {
            CURRENT_RING_TS = now;
        }
        let mut product = 1.0;
        for i in 0..PREDEFINED_RINGS[ring_index].len() {
            let (pair, dir) = PREDEFINED_RINGS[ring_index][i];
            let rate = unsafe { TRADING_PAIR_RATES[pair as usize][dir as usize] };
            product *= rate;
            let price = match dir {
                EdgeDirection::Forward => 1.0 / rate,
                EdgeDirection::Reverse => rate,
            };
            let mut price_buffer = ryu::Buffer::new();
            let price_str = price_buffer.format(price);
            let mut update_time_buffer = itoa::Buffer::new();
            let update_time_str =
                update_time_buffer.format(unsafe { TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] });
            let mut event_time_buffer = itoa::Buffer::new();
            let event_time_str =
                event_time_buffer.format(unsafe { TRADING_PAIR_RATE_EVENT_TIME[pair as usize] });
            let mut from_buffer = itoa::Buffer::new();
            let from_str = from_buffer.format(unsafe { TRADING_PAIR_RATE_FROM[pair as usize] });
            logln!(
                "{} dir: {} p: {} q: {} u_ts: {} e_ts: {} from: {} ",
                pair.to_str(),
                dir.to_str(),
                price_str,
                unsafe { ORDER_QUANTITIES[i] },
                update_time_str,
                event_time_str,
                from_str
            );
            let depth = unsafe { ORDER_BOOKS[pair as usize] };
            logln!("Depth: {}", depth);
        }
        let mut ryu_buffer = ryu::Buffer::new();
        let max_product_str = ryu_buffer.format(product);
        logln!("Expect rate: {}", max_product_str);
    }

    pub fn update_rate_by_ws_book_ticker(book_ticker: &BookTicker) -> bool {
        let pair: TradingPair = book_ticker.symbol.into();
        unsafe {
            // 如果已经由trade标记为无效，则暂时不用json bbo了
            if TRADING_PAIR_RATE_FROM[pair as usize] == 0 {
                if book_ticker.update_id > TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                    TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = book_ticker.update_id;
                }
                return false;
            }
            if book_ticker.update_id <= TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                return false;
            }
            let now = system_now_in_us();
            TRADING_PAIR_RATE_FROM[pair as usize] = 4;
            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                1.0 / book_ticker.ask_price;
            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] =
                book_ticker.bid_price;
            TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = now;
            TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = book_ticker.update_id;
        }
        true
    }

    // #[perf_macro::measure]
    #[inline(always)]
    pub fn update_rate(book_ticker: &SbeBookTicker) -> bool {
        let pair: TradingPair = book_ticker.symbol.into();
        unsafe {
            if book_ticker.book_update_id <= TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                return false;
            }
            let now = system_now_in_us();
            if (now - book_ticker.event_time) > 1000 {
                TRADING_PAIR_RATE_FROM[pair as usize] = 0;
                if book_ticker.book_update_id > TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                    TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = book_ticker.book_update_id;
                }
                return false;
            }
            TRADING_PAIR_RATE_FROM[pair as usize] = 1;
            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                1.0 / book_ticker.ask_price;
            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] =
                book_ticker.bid_price;
            TRADING_PAIR_RATE_EVENT_TIME[pair as usize] = book_ticker.event_time;
            TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = system_now_in_us();
            TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = book_ticker.book_update_id;
        }
        true
    }

    pub fn update_orderbook_by_updates(diff: &SbeDepthUpdate) -> bool {
        unsafe {
            let now = system_now_in_us();
            let pair: TradingPair = diff.symbol.into();
            let order_book_ptr = std::ptr::addr_of_mut!(ORDER_BOOKS);
            if !(*order_book_ptr)[pair as usize].apply_diff(
                diff.final_update_id,
                &diff.bid_updates,
                &diff.ask_updates,
                diff.event_time,
            ) {
                return false;
            }
            if diff.final_update_id > TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                if (now - diff.event_time) > 3_000 {
                    TRADING_PAIR_RATE_FROM[pair as usize] = 0;
                    return false;
                }
                TRADING_PAIR_RATE_FROM[pair as usize] = 2;
                TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = diff.final_update_id;
                TRADING_PAIR_RATE_EVENT_TIME[pair as usize] = diff.event_time;
                TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = now;
                if let Some(bid) = (*order_book_ptr)[pair as usize].best_bid() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] = bid.price;
                }
                if let Some(ask) = (*order_book_ptr)[pair as usize].best_ask() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                        1.0 / ask.price;
                }
                return true;
            }
        }
        return false;
    }

    pub fn update_orderbook_by_ws_updates(diff: &DepthUpdate) -> bool {
        unsafe {
            let now = system_now_in_us();
            let pair: TradingPair = diff.symbol.into();
            let order_book_ptr = std::ptr::addr_of_mut!(ORDER_BOOKS);
            if !(*order_book_ptr)[pair as usize].apply_diff(
                diff.final_update_id,
                &diff.bid_updates,
                &diff.ask_updates,
                diff.event_time,
            ) {
                return false;
            }
            if diff.final_update_id > TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                if (now - diff.event_time) > 3_000 {
                    TRADING_PAIR_RATE_FROM[pair as usize] = 0;
                    return false;
                }
                TRADING_PAIR_RATE_FROM[pair as usize] = 5;
                TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = diff.final_update_id;
                TRADING_PAIR_RATE_EVENT_TIME[pair as usize] = diff.event_time;
                TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = now;
                if let Some(bid) = (*order_book_ptr)[pair as usize].best_bid() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] = bid.price;
                }
                if let Some(ask) = (*order_book_ptr)[pair as usize].best_ask() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                        1.0 / ask.price;
                }
                return true;
            }
        }
        return false;
    }

    pub fn update_orderbook_by_snapshot(snapshot: &SbeDepthSnapshot) -> bool {
        unsafe {
            let now = system_now_in_us();
            let pair: TradingPair = snapshot.symbol.into();
            let order_book_ptr = std::ptr::addr_of_mut!(ORDER_BOOKS);
            if !(*order_book_ptr)[pair as usize].apply_snapshot(
                snapshot.last_update_id,
                &snapshot.bids,
                &snapshot.asks,
                snapshot.event_time,
            ) {
                return false;
            }
            if snapshot.last_update_id > TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                if (now - snapshot.event_time) > 3_000 {
                    TRADING_PAIR_RATE_FROM[pair as usize] = 0;
                    return false;
                }
                TRADING_PAIR_RATE_FROM[pair as usize] = 3;
                TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = snapshot.last_update_id;
                TRADING_PAIR_RATE_EVENT_TIME[pair as usize] = snapshot.event_time;
                TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = now;
                if let Some(bid) = (*order_book_ptr)[pair as usize].best_bid() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] = bid.price;
                }
                if let Some(ask) = (*order_book_ptr)[pair as usize].best_ask() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                        1.0 / ask.price;
                }
                return true;
            }
            false
        }
    }

    pub fn update_orderbook_by_ws_snapshot(snapshot: &DepthSnapshot) -> bool {
        unsafe {
            let now = system_now_in_us();
            let pair: TradingPair = TradingPair::from_lower_case(snapshot.symbol);
            let order_book_ptr = std::ptr::addr_of_mut!(ORDER_BOOKS);
            if !(*order_book_ptr)[pair as usize].apply_snapshot(
                snapshot.last_update_id,
                &snapshot.bids,
                &snapshot.asks,
                0, // no event time
            ) {
                return false;
            }
            if snapshot.last_update_id > TRADING_PAIR_RATE_UPDATE_ID[pair as usize] {
                TRADING_PAIR_RATE_FROM[pair as usize] = 6;
                TRADING_PAIR_RATE_UPDATE_ID[pair as usize] = snapshot.last_update_id;
                TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = now;
                if let Some(bid) = (*order_book_ptr)[pair as usize].best_bid() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] = bid.price;
                }
                if let Some(ask) = (*order_book_ptr)[pair as usize].best_ask() {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                        1.0 / ask.price;
                }
                return true;
            }
            false
        }
    }

    pub fn update_rate_by_trades(trade: SbeTrade) {
        let pair: TradingPair = trade.symbol.into();
        // 无脑信任trade比bbo或者depth更快
        // trade last trade price 如果不在[bid1, asks1]之间，则标记为 invalid
        let price = trade.last_trade_price;
        unsafe {
            let bid1 = TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize];
            let ask1 = 1.0 / TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize];
            if !(price > bid1 && price < ask1) {
                TRADING_PAIR_RATE_FROM[pair as usize] = 0;
            }
            TRADING_PAIR_RATE_EVENT_TIME[pair as usize] = trade.event_time;
        }
    }

    pub fn update_rate_by_agg_trades(trade: AggTrade) {
        let pair: TradingPair = trade.symbol.into();
        let update_time = unsafe { TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] };

        // 统一使用event_time对比
        if trade.event_time > update_time {
            // trade last trade priru 如果不在[bid1, asks1]之间，则标记为 invalid
            let price = trade.price;
            unsafe {
                let bid1 = TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize];
                let ask1 = 1.0 / TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize];
                if !(price > bid1 && price < ask1) {
                    TRADING_PAIR_RATE_FROM[pair as usize] = 0;
                }
            }
        }
    }

    // #[perf_macro::measure]
    #[inline(always)]
    pub fn compute_orders(circle_index: usize) -> Option<usize> {
        let ring = PREDEFINED_RINGS[circle_index];
        for try_count in 0..100 {
            let mut init_amount: f64 = 0.0;
            let mut last_asset_q = 0.0f64;
            let mut i = 0;
            while i < ring.len() {
                let (pair, direction) = ring[i];
                let order_filters = ORDER_FILTERS[pair as usize];
                if i == 0 {
                    if try_count == 0 {
                        let init_q = match pair.quote() {
                            Currency::XETH => 0.004f64,
                            Currency::XBNB => 0.02f64,
                            Currency::XBTC => 0.00015f64,
                            _ => 15.0f64,
                        };
                        match direction {
                            EdgeDirection::Forward => unsafe {
                                let base_asset_q = quote_to_base_quantity(init_q, pair);
                                let base_asset_q_adjusted = adjust_quantity(
                                    base_asset_q,
                                    init_q,
                                    order_filters[ORDER_FILTER_MIN_ORDER_QTY_INDEX],
                                    order_filters[ORDER_FILTER_LOT_SIZE_INDEX],
                                    TRADING_PAIR_RATES[pair as usize]
                                        [EdgeDirection::Forward as usize],
                                    order_filters[ORDER_FILTER_MIN_NOTIONAL_INDEX],
                                )?;
                                ORDER_QUANTITIES[i] = base_asset_q_adjusted;
                                init_amount = base_to_quote_quantity(ORDER_QUANTITIES[i], pair);
                                last_asset_q = ORDER_QUANTITIES[i];
                            },
                            EdgeDirection::Reverse => unsafe {
                                let base_asset_q = quote_to_base_quantity(init_q, pair);
                                let base_asset_q_adjusted = adjust_quantity(
                                    base_asset_q,
                                    init_q,
                                    order_filters[ORDER_FILTER_MIN_ORDER_QTY_INDEX],
                                    order_filters[ORDER_FILTER_LOT_SIZE_INDEX],
                                    TRADING_PAIR_RATES[pair as usize]
                                        [EdgeDirection::Forward as usize],
                                    order_filters[ORDER_FILTER_MIN_NOTIONAL_INDEX],
                                )?;
                                last_asset_q = base_to_quote_quantity(base_asset_q_adjusted, pair);
                                ORDER_QUANTITIES[i] = base_asset_q_adjusted;
                                init_amount = base_asset_q_adjusted;
                            },
                        }
                    } else {
                        unsafe {
                            ORDER_QUANTITIES[i] += order_filters[ORDER_FILTER_LOT_SIZE_INDEX];
                            match direction {
                                EdgeDirection::Forward => {
                                    init_amount = base_to_quote_quantity(ORDER_QUANTITIES[i], pair);
                                    last_asset_q = ORDER_QUANTITIES[i];
                                }
                                EdgeDirection::Reverse => {
                                    init_amount = base_to_quote_quantity(ORDER_QUANTITIES[i], pair);
                                    last_asset_q =
                                        base_to_quote_quantity(ORDER_QUANTITIES[i], pair);
                                }
                            }
                        }
                    }
                    i += 1;
                    continue;
                }
                let (base_q, quote_q) = match direction {
                    EdgeDirection::Forward => {
                        (quote_to_base_quantity(last_asset_q, pair), last_asset_q)
                    }
                    EdgeDirection::Reverse => {
                        (last_asset_q, base_to_quote_quantity(last_asset_q, pair))
                    }
                };
                let q = match adjust_quantity(
                    base_q,
                    quote_q,
                    order_filters[ORDER_FILTER_MIN_ORDER_QTY_INDEX],
                    order_filters[ORDER_FILTER_LOT_SIZE_INDEX],
                    unsafe { TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] },
                    order_filters[ORDER_FILTER_MIN_NOTIONAL_INDEX],
                ) {
                    Some(r) => r,
                    None => break,
                };
                unsafe {
                    ORDER_QUANTITIES[i] = q;
                }
                last_asset_q = match direction {
                    EdgeDirection::Forward => q,
                    EdgeDirection::Reverse => base_to_quote_quantity(q, pair),
                };
                i += 1;
            }
            if i < ring.len() {
                continue;
            }

            match ring[ring.len() - 1].1 {
                EdgeDirection::Forward => unsafe {
                    if ((ORDER_QUANTITIES[ring.len() - 1] - init_amount) / init_amount).abs() < 0.1
                    {
                        return Some(ring.len());
                    } else {
                        return None;
                    }
                },
                EdgeDirection::Reverse => {
                    if ((last_asset_q - init_amount) / init_amount).abs() < 0.1 {
                        return Some(ring.len());
                    } else {
                        return None;
                    }
                }
            }
        }
        None
    }

    // #[perf_macro::measure]
    #[inline(always)]
    pub fn check_arbitrage(now: u64, pair: TradingPair) -> Option<usize> {
        if !Self::check_arbitrage_conditions(now) {
            return None;
        }
        let ring_indices = TRADING_PAIR_TO_RING_INDEX[pair as usize];
        let mut max_product = 0.0f64;
        let mut result: usize = 0;
        let system_now = system_now_in_us();
        for index in ring_indices {
            let ring = PREDEFINED_RINGS[*index as usize];
            let mut product = 1.0;
            // let mut total_fee = 0.0;

            for &(pair, dir) in ring {
                if unsafe { TRADING_PAIR_RATE_FROM[pair as usize] } == 0 {
                    product = 0.0;
                    break;
                }
                let rate = unsafe { TRADING_PAIR_RATES[pair as usize][dir as usize] };
                if rate == 0.0 {
                    product = 0.0;
                    break;
                }
                let orderbook = unsafe { ORDER_BOOKS[pair as usize] };
                if (system_now - orderbook.event_time) < 500_000 {
                    match dir {
                        EdgeDirection::Forward => {
                            match orderbook.find_nearest_ask_level(1.0 / rate) {
                                Some(level) => {
                                    // if level.0 > 4 {
                                    //     product = 0.0;
                                    //     break;
                                    // }
                                    let amount = orderbook.asks_amount_at(level.0);
                                    match pair.quote() {
                                        Currency::XUSDC | Currency::XUSDT => {
                                            if amount < 500.0 {
                                                product = 0.0;
                                                break;
                                            }
                                        }
                                        _ => {
                                            let value_pair =
                                                TradingPair::from((pair.quote(), Currency::XUSDT));
                                            let value_rate = unsafe {
                                                TRADING_PAIR_RATES[value_pair as usize]
                                                    [EdgeDirection::Reverse as usize]
                                            };
                                            if amount * value_rate < 500.0 {
                                                product = 0.0;
                                                break;
                                            }
                                        }
                                    }
                                    // if pair.base() != Currency::XETH
                                    //     && pair.base() != Currency::XSUI
                                    //     && orderbook.spread_in_bp() < 0.8
                                    // {
                                    //     logln!(
                                    //         "spread too low: {} {}",
                                    //         pair.to_str(),
                                    //         orderbook.spread_in_bp()
                                    //     );
                                    //     product = 0.0;
                                    //     break;
                                    // }
                                }
                                None => {
                                    product = 0.0;
                                    break;
                                }
                            }
                        }
                        EdgeDirection::Reverse => match orderbook.find_nearest_bid_level(rate) {
                            Some(level) => {
                                // if level.0 > 4 {
                                //     product = 0.0;
                                //     break;
                                // }
                                let amount = orderbook.bids_amount_at(level.0);
                                match pair.quote() {
                                    Currency::XUSDC | Currency::XUSDT => {
                                        if amount < 500.0 {
                                            product = 0.0;
                                            break;
                                        }
                                    }
                                    _ => {
                                        let value_pair =
                                            TradingPair::from((pair.quote(), Currency::XUSDT));
                                        let value_rate = unsafe {
                                            TRADING_PAIR_RATES[value_pair as usize]
                                                [EdgeDirection::Reverse as usize]
                                        };
                                        if amount * value_rate < 500.0 {
                                            product = 0.0;
                                            break;
                                        }
                                    }
                                }
                                // if pair.base() != Currency::XETH
                                //     && pair.base() != Currency::XSUI
                                //     && orderbook.spread_in_bp() < 0.8
                                // {
                                //     logln!(
                                //         "spread too low: {} {}",
                                //         pair.to_str(),
                                //         orderbook.spread_in_bp()
                                //     );
                                //     product = 0.0;
                                //     break;
                                // }
                            }
                            None => {
                                product = 0.0;
                                break;
                            }
                        },
                    }
                } else {
                    product = 0.0;
                    break;
                }

                product *= rate;

                // 计算该交易对的实际手续费 (taker fee * BNB 折扣)
                // let taker_fee = unsafe { TRADING_FEES[pair as usize][TAKER_FEE_INDEX] };
                // let actual_fee = taker_fee * BNB_DISCOUNT;
                // total_fee += if taker_fee == 0.0 {
                //     0.0
                // } else {
                //     0.0001725 * 1.1
                // };
            }
            // let threshold = 1.0 + total_fee;
            if product > max_product {
                result = *index as usize;
                max_product = product;
            }
        }
        logln!("max product: {}", max_product);
        if (max_product > 1.0007 && PREDEFINED_RINGS[result].len() >= 3)
            || (max_product > 1.0006 && PREDEFINED_RINGS[result].len() == 3)
        {
            Some(result)
        } else {
            None
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn find_ring(pair1: TradingPair, pair2: TradingPair, pair3: TradingPair) -> Option<usize> {
        for i in 0..PREDEFINED_RINGS.len() {
            let ring = PREDEFINED_RINGS[i];
            if ring.len() >= 3 && ring[0].0 == pair1 && ring[1].0 == pair2 && ring[2].0 == pair3 {
                return Some(i);
            }
        }
        None
    }

    fn setup_test_rates() {
        // 清零所有汇率
        unsafe {
            use std::ptr::addr_of_mut;
            let rates_ptr = addr_of_mut!(TRADING_PAIR_RATES);
            for i in 0..28 {
                // 硬编码长度以避免引用静态变量
                (*rates_ptr)[i][EdgeDirection::Forward as usize] = 0.0;
                (*rates_ptr)[i][EdgeDirection::Reverse as usize] = 0.0;
            }
            // 清零订单数量
            let quantities_ptr = addr_of_mut!(ORDER_QUANTITIES);
            for i in 0..10 {
                // 硬编码长度以避免引用静态变量
                (*quantities_ptr)[i] = 0.0;
            }
        }
    }

    #[test]
    #[should_panic]
    fn test_compute_orders_invalid_ring_index() {
        setup_test_rates();
        // 测试无效的环索引
        let _result = ArbitrageEngine::compute_orders(9999);
        // 这应该会 panic 或返回 None，取决于实现
        // 由于使用了 unsafe 访问，这可能会导致 panic
    }

    #[test]
    fn test_compute_orders_no_rates() {
        setup_test_rates();
        // 测试没有设置汇率的情况
        let result = ArbitrageEngine::compute_orders(0);
        assert_eq!(result, None);
    }
}
