use memchr;
use std::str;

use crate::TradingPair;

#[derive(Debug)]
pub struct OrderId {
    pub order_create_time: u64, // it it not an timestamp, but a time represents by cpu circles
    pub ring_index: usize,
    pub edge_index: usize,
    pub is_testing: bool,
}

pub struct OrderCreated {
    pub order_id: OrderId,
    pub symbol: TradingPair,
}

impl OrderCreated {
    pub fn new(order_id: OrderId, symbol: TradingPair) -> Self {
        OrderCreated { order_id, symbol }
    }
}

#[derive(Debug)]
pub struct OrderUpdate {
    pub order_id: OrderId,
    pub fill_price: f64,
    pub fill_quantity: f64,
    pub symbol: TradingPair,
    pub status: String,          // 执行状态：NEW, TRADE, FILLED 等
    pub orig_quantity: f64,      // 原始订单数量
    pub executed_quantity: f64,  // 已执行数量
    pub remaining_quantity: f64, // 剩余数量
    pub order_submited_time: u64,
    pub order_trasaction_time: u64,
}

pub enum OrderResponse {
    OrderCreated(OrderCreated),
    OrderUpdate(OrderUpdate),
    OrderError(String),
}

impl From<&str> for OrderId {
    fn from(s: &str) -> Self {
        let parts = s.split('-').collect::<Vec<&str>>();
        let order_create_time = match parts[0].parse::<u64>() {
            Ok(n) => n,
            Err(e) => {
                panic!("Got unexpect order id: {}, error: {}", s, e);
            }
        };
        let ring_index = parts[1].parse::<usize>().unwrap();
        let edge_index = parts[2].parse::<usize>().unwrap();
        // 如果有第4个部分，则解析 is_testing 标志，否则默认为 false
        let is_testing = if parts.len() > 3 {
            parts[3] == "1"
        } else {
            false
        };
        OrderId {
            order_create_time,
            ring_index,
            edge_index,
            is_testing,
        }
    }
}

impl OrderUpdate {
    pub fn new(order_id: OrderId, symbol: TradingPair, status: String) -> Self {
        OrderUpdate {
            order_id,
            fill_price: 0.0,
            fill_quantity: 0.0,
            symbol,
            status,
            orig_quantity: 0.0,
            executed_quantity: 0.0,
            remaining_quantity: 0.0,
            order_submited_time: 0,
            order_trasaction_time: 0,
        }
    }
}

// #[perf_macro::measure]
pub fn parse_order_update(input: &[u8]) -> Option<OrderResponse> {
    let error_pattern2 = b"\"error\":{";

    let error_start = memchr::memmem::find(input, error_pattern2);

    if let Some(error_start) = error_start {
        let msg_pattern2 = b"\"msg\":\"";
        let msg_start =
            if let Some(start) = memchr::memmem::find(&input[error_start..], msg_pattern2) {
                Some(error_start + start + msg_pattern2.len())
            } else {
                None
            };

        if let Some(msg_start) = msg_start {
            if let Some(msg_end) = memchr::memchr(b'"', &input[msg_start..]) {
                let error_msg = &input[msg_start..msg_start + msg_end];
                let error_msg: &str = unsafe { std::mem::transmute(error_msg) };
                return Some(OrderResponse::OrderError(error_msg.to_string()));
            }
        }
        // 如果无法解析具体错误信息，返回通用错误
        return Some(OrderResponse::OrderError("Order failed".to_string()));
    }

    let order_response_pattern = b"tOrderId\":\"";
    match memchr::memmem::find(input, order_response_pattern) {
        Some(start) => {
            let start = start + order_response_pattern.len();
            let end = memchr::memchr(b'"', &input[start..])?;
            let order_id = &input[start..start + end];
            let order_id: &str = unsafe { std::mem::transmute(order_id) };
            let order_id: OrderId = order_id.into();

            // 解析 symbol
            let start = memchr::memmem::find(input, b"\"symbol\":\"")? + 10;
            let end = memchr::memchr(b'"', &input[start..])?;
            let symbol = &input[start..start + end];
            let symbol: &str = unsafe { std::mem::transmute(symbol) };
            let symbol = TradingPair::from(symbol);

            // 创建 OrderCreated
            let order_created = OrderCreated::new(order_id, symbol);

            Some(OrderResponse::OrderCreated(order_created))
        }
        None => {
            let order_update_pattern = b"executionReport";
            let _execution_report_pos = memchr::memmem::find(input, order_update_pattern)?;
            let status_pattern = b"x\":\"";
            let status_start = memchr::memmem::find(input, status_pattern)?;
            let start = status_start + status_pattern.len();
            let end = memchr::memchr(b'"', &input[start..])?;
            let exec_status = &input[start..start + end];
            let exec_status: &str = unsafe { std::mem::transmute(exec_status) };
            // Handle any execution status (NEW, TRADE, FILLED, REJECTED, EXPIRED, CANCELED, etc.)
            if exec_status == "TRADE"
                || exec_status == "NEW"
                || exec_status == "FILLED"
                || exec_status == "REJECTED"
                || exec_status == "EXPIRED"
                || exec_status == "CANCELED"
            {
                let order_id_pattern = b"\"c\":\"";
                let start = memchr::memmem::find(input, order_id_pattern)? + order_id_pattern.len();
                let end = memchr::memchr(b'"', &input[start..])?;
                let order_id = &input[start..start + end];
                let order_id: &str = unsafe { std::mem::transmute(order_id) };
                let order_id: OrderId = order_id.into();

                let symbol_pattern = b"\"s\":\"";
                let symbol = memchr::memmem::find(input, symbol_pattern)? + symbol_pattern.len();
                let end = memchr::memchr(b'"', &input[symbol..])?;
                let symbol = &input[symbol..symbol + end];
                let symbol: &str = unsafe { std::mem::transmute(symbol) };
                let symbol = TradingPair::from(symbol);

                let mut order_update = OrderUpdate::new(order_id, symbol, exec_status.to_string());

                // 解析原始数量 (q field) - 支持有空格和无空格的格式
                let orig_qty_pattern1 = b"\"q\": \""; // 有空格的格式
                let orig_qty_pattern2 = b"\"q\":\""; // 无空格的格式

                let (start_pos, pattern_len) =
                    if let Some(start) = memchr::memmem::find(input, orig_qty_pattern1) {
                        (start, orig_qty_pattern1.len())
                    } else if let Some(start) = memchr::memmem::find(input, orig_qty_pattern2) {
                        (start, orig_qty_pattern2.len())
                    } else {
                        (0, 0) // 标记未找到
                    };

                if pattern_len > 0 {
                    let start = start_pos + pattern_len;
                    if let Some(end) = memchr::memchr(b'"', &input[start..]) {
                        let orig_qty = &input[start..start + end];
                        let orig_qty: &str = unsafe { std::mem::transmute(orig_qty) };
                        if let Ok(qty) = orig_qty.parse::<f64>() {
                            order_update.orig_quantity = qty;
                            // 初始化剩余数量为原始数量
                            order_update.remaining_quantity = qty;
                        }
                    }
                }

                // 解析已执行数量 (z field) - 支持有空格和无空格的格式
                let executed_qty_pattern1 = b"\"z\": \""; // 有空格的格式
                let executed_qty_pattern2 = b"\"z\":\""; // 无空格的格式

                let (start_pos, pattern_len) =
                    if let Some(start) = memchr::memmem::find(input, executed_qty_pattern1) {
                        (start, executed_qty_pattern1.len())
                    } else if let Some(start) = memchr::memmem::find(input, executed_qty_pattern2) {
                        (start, executed_qty_pattern2.len())
                    } else {
                        (0, 0) // 标记未找到
                    };

                if pattern_len > 0 {
                    let start = start_pos + pattern_len;
                    if let Some(end) = memchr::memchr(b'"', &input[start..]) {
                        let executed_qty = &input[start..start + end];
                        let executed_qty: &str = unsafe { std::mem::transmute(executed_qty) };
                        if let Ok(qty) = executed_qty.parse::<f64>() {
                            order_update.executed_quantity = qty;
                            // 重新计算剩余数量
                            order_update.remaining_quantity = order_update.orig_quantity - qty;
                        }
                    }
                }

                // Parse fill price and quantity for TRADE status
                if exec_status == "TRADE" {
                    // Parse fill price (L field)
                    let price_pattern = b"\"L\":\"";
                    if let Some(start) = memchr::memmem::find(input, price_pattern) {
                        let start = start + price_pattern.len();
                        if let Some(end) = memchr::memchr(b'"', &input[start..]) {
                            let fill_price = &input[start..start + end];
                            let fill_price: &str = unsafe { std::mem::transmute(fill_price) };
                            if let Ok(price) = fill_price.parse::<f64>() {
                                order_update.fill_price = price;
                            }
                        }
                    }

                    // Parse fill quantity (l field - lowercase L)
                    let qty_pattern = b"\"l\":\"";
                    if let Some(start) = memchr::memmem::find(input, qty_pattern) {
                        let start = start + qty_pattern.len();
                        if let Some(end) = memchr::memchr(b'"', &input[start..]) {
                            let fill_qty = &input[start..start + end];
                            let fill_qty: &str = unsafe { std::mem::transmute(fill_qty) };
                            if let Ok(qty) = fill_qty.parse::<f64>() {
                                order_update.fill_quantity = qty;
                            }
                        }
                    }
                    let order_transaction_time_pattern = b"\"T\":";
                    let start = memchr::memmem::find(input, order_transaction_time_pattern)?
                        + order_transaction_time_pattern.len();
                    let end = memchr::memchr(b',', &input[start..])?;
                    let order_transaction_time = &input[start..start + end];
                    let order_transaction_time: &str =
                        unsafe { std::mem::transmute(order_transaction_time) };
                    order_update.order_trasaction_time = order_transaction_time.parse().unwrap();
                }
                if exec_status == "NEW" {
                    let order_creat_patter = b"\"O\":";
                    let start =
                        memchr::memmem::find(input, order_creat_patter)? + order_creat_patter.len();
                    let end = memchr::memchr(b',', &input[start..])?;
                    let order_create_time = &input[start..start + end];
                    let order_create_time: &str = unsafe { std::mem::transmute(order_create_time) };
                    order_update.order_submited_time = order_create_time.parse().unwrap();
                }
                Some(OrderResponse::OrderUpdate(order_update))
            } else {
                None
            }
        }
    }
}

#[cfg(test)]
mod tests {

    use super::*;
    #[test]
    fn test_parse_order_update() {
        let data = r#"
{
  "id": "***************",
  "status": 200,
  "result": {
    "symbol":"BTCUSDT",
    "orderId": 43735702641,
    "orderListId": -1,
    "clientOrderId":"***************-0-0",
    "transactTime": *************,
    "price": "109624.********",
    "origQty": "0.00011000",
    "executedQty": "0.********",
    "origQuoteOrderQty": "0.********",
    "cummulativeQuoteQty": "0.********",
    "status": "EXPIRED",
    "timeInForce": "IOC",
    "type": "LIMIT",
    "side": "BUY",
    "workingTime": *************,
    "fills": [],
    "selfTradePreventionMode": "EXPIRE_MAKER"
  },
  "rateLimits": [
    {
      "rateLimitType": "ORDERS",
      "interval": "SECOND",
      "intervalNum": 10,
      "limit": 100,
      "count": 90
    },
    {
      "rateLimitType": "ORDERS",
      "interval": "DAY",
      "intervalNum": 1,
      "limit": 200000,
      "count": 40240
    },
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 6000,
      "count": 141
    }
  ]
}

        "#;
        let r = parse_order_update(data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderCreated(order_created) = r.unwrap() else {
            panic!("Expected OrderCreated");
        };
        assert_eq!(order_created.order_id.order_create_time, ***************);
        assert_eq!(order_created.order_id.ring_index, 0);
        assert_eq!(order_created.order_id.edge_index, 0);
        assert_eq!(order_created.symbol, TradingPair::XBTCUSDT);
    }

    #[test]
    fn test_parse_order_update_optimized() {
        let data = r#"
{
  "event": {
    "e": "executionReport",
    "E": *************,
    "s":"BTCUSDT",
    "c":"***************-0-0",
    "S": "BUY",
    "o": "LIMIT",
    "f": "IOC",
    "q": "0.00011000",
    "p":"109624.********",
    "P": "0.********",
    "F": "0.********",
    "g": -1,
    "C":"",
    "x":"TRADE",
    "X":"TRADE",
    "r": "NONE",
    "i": ***********,
    "l": "0.********",
    "z": "0.********",
    "L":"0.********",
    "n": "0",
    "N": null,
    "T": *************,
    "t": -1,
    "I": 93070863163,
    "w": false,
    "m": false,
    "M": false,
    "O": *************,
    "Z": "0.********",
    "Y": "0.********",
    "Q": "0.********",
    "W": *************,
    "V": "EXPIRE_MAKER"
  }
}

        "#;
        let r = parse_order_update(data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderUpdate(order_update) = r.unwrap() else {
            panic!("Expected OrderUpdate");
        };
        assert_eq!(order_update.order_id.order_create_time, ***************);
        assert_eq!(order_update.order_id.ring_index, 0);
        assert_eq!(order_update.order_id.edge_index, 0);
        assert_eq!(order_update.symbol, TradingPair::XBTCUSDT);
    }

    #[test]
    fn test_parse_order_update_new_status() {
        let data = r#"
{
  "event": {
    "e": "executionReport",
    "E": *************,
    "s":"BTCUSDT",
    "c":"***************-0-0",
    "S": "BUY",
    "o": "LIMIT",
    "f": "IOC",
    "q": "0.00011000",
    "p":"109624.********",
    "x":"NEW",
    "X":"NEW",
    "i": ***********,
    "O": *************
  }
}
        "#;
        let r = parse_order_update(data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderUpdate(order_update) = r.unwrap() else {
            panic!("Expected OrderUpdate");
        };
        assert_eq!(order_update.order_id.order_create_time, ***************);
        assert_eq!(order_update.order_id.ring_index, 0);
        assert_eq!(order_update.order_id.edge_index, 0);
        assert_eq!(order_update.symbol, TradingPair::XBTCUSDT);
        assert_eq!(order_update.fill_price, 0.0); // No fill price for NEW status
        assert_eq!(order_update.fill_quantity, 0.0); // No fill quantity for NEW status
        assert_eq!(order_update.orig_quantity, 0.00011); // Original quantity parsed
        assert_eq!(order_update.executed_quantity, 0.0); // No execution yet
        assert_eq!(order_update.remaining_quantity, 0.00011); // All remaining
    }

    #[test]
    fn test_parse_order_update_trade_status() {
        let data = r#"
{
  "event": {
    "e": "executionReport",
    "E": *************,
    "s":"BTCUSDT",
    "c":"***************-0-0",
    "S": "BUY",
    "o": "LIMIT",
    "f": "IOC",
    "q": "0.00011000",
    "p":"109624.********",
    "x":"TRADE",
    "X":"FILLED",
    "i": ***********,
    "l":"0.00011000",
    "L":"109624.********",
    "z":"0.00011000",
    "O": *************
  }
}
        "#;
        let r = parse_order_update(data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderUpdate(order_update) = r.unwrap() else {
            panic!("Expected OrderUpdate");
        };
        assert_eq!(order_update.order_id.order_create_time, ***************);
        assert_eq!(order_update.order_id.ring_index, 0);
        assert_eq!(order_update.order_id.edge_index, 0);
        assert_eq!(order_update.symbol, TradingPair::XBTCUSDT);
        assert_eq!(order_update.fill_price, 109624.0); // Fill price for TRADE status
        assert_eq!(order_update.fill_quantity, 0.00011); // Fill quantity for TRADE status
        assert_eq!(order_update.orig_quantity, 0.00011); // Original quantity
        assert_eq!(order_update.executed_quantity, 0.00011); // Fully executed
        assert_eq!(order_update.remaining_quantity, 0.0); // Nothing remaining
    }

    #[test]
    fn test_order_update_status_field() {
        let new_data = r#"
{
  "event": {
    "e": "executionReport",
    "E": *************,
    "s":"BTCUSDT",
    "c":"***************-0-0",
    "x":"NEW",
    "X":"NEW",
    "i": ***********,
    "O": *************
  }
}
        "#;
        let r = parse_order_update(new_data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderUpdate(order_update) = r.unwrap() else {
            panic!("Expected OrderUpdate");
        };
        assert_eq!(order_update.status, "NEW");

        let trade_data = r#"
{
  "event": {
    "e": "executionReport",
    "E": *************,
    "s":"BTCUSDT",
    "c":"***************-0-0",
    "x":"TRADE",
    "X":"FILLED",
    "i": ***********,
    "l":"0.00011000",
    "L":"109624.********",
    "O": *************
  }
}
        "#;
        let r = parse_order_update(trade_data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderUpdate(order_update) = r.unwrap() else {
            panic!("Expected OrderUpdate");
        };
        assert_eq!(order_update.status, "TRADE");
    }

    #[test]
    fn test_parse_order_update_error_statuses() {
        // Test REJECTED status
        let rejected_data = r#"
{
  "event": {
    "e": "executionReport",
    "E": *************,
    "s":"BTCUSDT",
    "c":"***************-0-0",
    "S": "BUY",
    "o": "LIMIT",
    "f": "IOC",
    "q": "0.00011000",
    "p":"109624.********",
    "x":"REJECTED",
    "X":"REJECTED",
    "i": ***********,
    "O": *************
  }
}
        "#;

        let r = parse_order_update(rejected_data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderUpdate(order_update) = r.unwrap() else {
            panic!("Expected OrderUpdate");
        };
        assert_eq!(order_update.status, "REJECTED");
        assert_eq!(order_update.order_id.order_create_time, ***************);

        // Test EXPIRED status
        let expired_data = r#"
{
  "event": {
    "e": "executionReport",
    "E": *************,
    "s":"BTCUSDT",
    "c":"***************-1-2",
    "S": "BUY",
    "o": "LIMIT",
    "f": "IOC",
    "q": "0.00011000",
    "p":"109624.********",
    "x":"EXPIRED",
    "X":"EXPIRED",
    "i": ***********,
    "O": *************
  }
}
        "#;

        let r = parse_order_update(expired_data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderUpdate(order_update) = r.unwrap() else {
            panic!("Expected OrderUpdate");
        };
        assert_eq!(order_update.status, "EXPIRED");
        assert_eq!(order_update.order_id.ring_index, 1);
        assert_eq!(order_update.order_id.edge_index, 2);

        // Test CANCELED status
        let canceled_data = r#"
{
  "event": {
    "e": "executionReport",
    "E": *************,
    "s":"BTCUSDT",
    "c":"***************-2-1",
    "S": "BUY",
    "o": "LIMIT",
    "f": "IOC",
    "q": "0.00011000",
    "p":"109624.********",
    "x":"CANCELED",
    "X":"CANCELED",
    "i": ***********,
    "O": *************
  }
}
        "#;

        let r = parse_order_update(canceled_data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderUpdate(order_update) = r.unwrap() else {
            panic!("Expected OrderUpdate");
        };
        assert_eq!(order_update.status, "CANCELED");
        assert_eq!(order_update.order_id.ring_index, 2);
        assert_eq!(order_update.order_id.edge_index, 1);
    }

    #[test]
    fn test_parse_order_error_insufficient_balance() {
        let error_data = r#"
{
  "id": "***************",
  "status": 400,
  "error": {
    "code": -2010,
    "msg": "Account has insufficient balance for requested action."
  },
  "rateLimits": [
    {
      "rateLimitType": "ORDERS",
      "interval": "SECOND",
      "intervalNum": 10,
      "limit": 50,
      "count": 13
    }
  ]
}
        "#;

        let r = parse_order_update(error_data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderError(error_msg) = r.unwrap() else {
            panic!("Expected OrderError");
        };
        assert_eq!(
            error_msg,
            "Account has insufficient balance for requested action."
        );
    }

    #[test]
    fn test_parse_order_expired_with_quantities() {
        let expired_data = r#"
{
  "event": {
    "e": "executionReport",
    "E": *************,
    "s":"BTCUSDT",
    "c":"***************-1-2",
    "S": "BUY",
    "o": "LIMIT",
    "f": "IOC",
    "q": "0.********",
    "p":"109624.********",
    "x":"EXPIRED",
    "X":"EXPIRED",
    "i": ***********,
    "z": "0.********",
    "O": *************
  }
}
        "#;

        let r = parse_order_update(expired_data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderUpdate(order_update) = r.unwrap() else {
            panic!("Expected OrderUpdate");
        };
        assert_eq!(order_update.status, "EXPIRED");
        assert_eq!(order_update.orig_quantity, 0.0005); // Original quantity
        assert_eq!(order_update.executed_quantity, 0.0002); // Partially executed
        assert!((order_update.remaining_quantity - 0.0003).abs() < 1e-10); // Remaining expired (with floating point tolerance)
        assert_eq!(order_update.order_id.ring_index, 1);
        assert_eq!(order_update.order_id.edge_index, 2);
    }

    #[test]
    fn test_parse_order_canceled_with_quantities() {
        let canceled_data = r#"
{
  "event": {
    "e": "executionReport",
    "E": *************,
    "s":"BTCUSDT",
    "c":"***************-2-1",
    "S": "SELL",
    "o": "LIMIT",
    "f": "GTC",
    "q": "0.00100000",
    "p":"110000.********",
    "x":"CANCELED",
    "X":"CANCELED",
    "i": ***********,
    "z": "0.********",
    "O": *************
  }
}
        "#;

        let r = parse_order_update(canceled_data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderUpdate(order_update) = r.unwrap() else {
            panic!("Expected OrderUpdate");
        };
        assert_eq!(order_update.status, "CANCELED");
        assert_eq!(order_update.orig_quantity, 0.001); // Original quantity
        assert_eq!(order_update.executed_quantity, 0.0); // Nothing executed
        assert_eq!(order_update.remaining_quantity, 0.001); // All canceled
        assert_eq!(order_update.order_id.ring_index, 2);
        assert_eq!(order_update.order_id.edge_index, 1);
    }
}
