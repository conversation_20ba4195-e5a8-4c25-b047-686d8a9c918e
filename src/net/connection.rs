use crate::{flush_logs, logln};

use super::message::Message;
use super::message::frame::Frame;
use super::message::http::{HttpResponse, IntoWebsocketHandshakeRequest, StatusCode};
use super::message::opcode::OpCode;
use super::result::{Error, Kind, Result};
use super::stream::TlsStream;
use super::utils::circular_buffer::CircularBuffer;
use super::utils::url::Url;
use mio::net::TcpStream;
use rustls::RootCertStore;
use rustls::pki_types::ServerName;
use std::borrow::Cow;
use std::collections::VecDeque;
use std::fs::File;
use std::io::BufReader;
use std::mem::take;
use std::sync::Arc;

use super::{CloseCode, Settings};

#[derive(Debug, <PERSON>ialEq, <PERSON><PERSON>, <PERSON><PERSON>, Co<PERSON>)]
#[repr(u8)]
pub enum State {
    TlsNegotiating,
    Handshaking,
    Open,
    RespondingClose,
    FinishedClose,
}

impl State {
    #[inline]
    pub fn is_closing(&self) -> bool {
        matches!(self, Self::RespondingClose)
    }
}

fn load_root(path: &str) -> RootCertStore {
    let mut store = RootCertStore::empty();
    let mut reader = BufReader::new(File::open(path).unwrap());
    let certs = rustls_pemfile::certs(&mut reader)
        .filter_map(std::result::Result::ok)
        .collect::<Vec<_>>();
    store.add_parsable_certificates(certs.into_iter());
    store
}

/// Connection is a struct that represents a connection to a server.
/// in_buffer is the buffer that stores the incoming data.
/// fragments is the buffer that stores the fragmented data. use reference of in_buffer
pub struct Connection<const IN_LEN: usize, const OUT_LEN: usize> {
    stream: TlsStream,
    state: State,
    fragments: VecDeque<Frame<'static>>,
    http_response: HttpResponse<'static>,
    in_buffer: Box<CircularBuffer<IN_LEN>>,
    out_buffer: CircularBuffer<OUT_LEN>,
    settings: Settings,
    pub url: Url,
    headers: Option<std::collections::HashMap<String, String>>,
}

impl<const IN_LEN: usize, const OUT_LEN: usize> Connection<IN_LEN, OUT_LEN> {
    #[allow(dead_code)]
    pub fn connect(url: Url, settings: Settings) -> Result<Self> {
        Self::connect_with_headers(url, settings, None)
    }

    pub fn connect_with_headers(
        url: Url,
        settings: Settings,
        headers: Option<std::collections::HashMap<String, String>>,
    ) -> Result<Self> {
        match url.to_socket_addr() {
            Ok(addr) => {
                if let Some(p) = addr.into_iter().next() {
                    let addr = if let Some(b) = url.socket_addr { b } else { p };
                    let sock = TcpStream::connect(addr)?;
                    sock.set_nodelay(settings.tcp_nodelay)?;
                    let root_store = if settings.test_cert_path.is_empty() {
                        rustls::RootCertStore::from_iter(
                            webpki_roots::TLS_SERVER_ROOTS.iter().cloned(),
                        )
                    } else {
                        load_root(&settings.test_cert_path)
                    };
                    let config = rustls::ClientConfig::builder()
                        .with_root_certificates(root_store)
                        .with_no_client_auth();
                    let dnsname = ServerName::try_from(url.host.clone()).unwrap();
                    let rc_config = Arc::new(config);
                    let conn = rustls::ClientConnection::new(rc_config, dnsname)?;
                    let tls = TlsStream::new(sock, conn);
                    let mut conn = Connection {
                        stream: tls,
                        state: State::TlsNegotiating,
                        http_response: HttpResponse::new(),
                        fragments: VecDeque::with_capacity(settings.fragments_capacity),
                        in_buffer: Box::new(CircularBuffer::<IN_LEN>::new()),
                        out_buffer: CircularBuffer::<OUT_LEN>::new(),
                        settings,
                        url: url.clone(),
                        headers: headers.clone(),
                    };
                    if !conn.url.is_http {
                        // should do this right now, avoid user to call send_message
                        let mut handshake_req = url.into_ws_handshake_request()?;

                        // 添加自定义头部
                        if let Some(custom_headers) = headers {
                            for (key, value) in custom_headers {
                                handshake_req.headers.add(key, value);
                            }
                        }

                        handshake_req.format(&mut conn.out_buffer)?;
                    }
                    return Ok(conn);
                }
                Err(Error::new(
                    Kind::NetWorkError,
                    "Unable to connect all addresses.",
                ))
            }
            Err(e) => Err(e),
        }
    }

    pub fn socket_mut(&mut self) -> &mut TcpStream {
        self.stream.socket_mut()
    }

    pub fn is_closing(&self) -> bool {
        self.state == State::RespondingClose
    }

    pub fn is_closed(&self) -> bool {
        self.state == State::FinishedClose
    }

    pub fn read<'b>(&mut self) -> Result<Option<Message<'b>>> {
        let mut loop_num = 0;
        loop {
            match self.stream.read_buf(&mut self.in_buffer)? {
                Some(0) => {
                    self.state = State::FinishedClose;
                    return Err(Error::new(Kind::ClosedByPeer, "Connection closed by peer."));
                }
                Some(_) => {
                    match self.try_parse_msg()? {
                        Some(msgs) => return Ok(Some(msgs)),
                        None => {
                            // 这里是一种防御性编程，防止死循环
                            loop_num += 1;
                            if loop_num > 100 {
                                panic!(
                                    "too many loops, in_buffer len: {}, in_buffer is full: {} url: {}",
                                    self.in_buffer.len(),
                                    self.in_buffer.is_full(),
                                    self.url
                                );
                            }
                        } // continue read buf until we get a message or read_buf return None
                    }
                }
                None => match self.try_parse_msg()? {
                    Some(msgs) => return Ok(Some(msgs)),
                    None => return Ok(None),
                },
            }
        }
    }

    pub fn shutdown(&mut self) -> Result<()> {
        if self.state != State::FinishedClose {
            self.stream.shutdown()?;
            self.state = State::FinishedClose;
        }
        Ok(())
    }

    pub fn reconnect(&mut self) -> Result<Connection<IN_LEN, OUT_LEN>> {
        self.shutdown()?;
        Self::connect_with_headers(
            self.url.clone(),
            self.settings.clone(),
            self.headers.clone(),
        )
    }

    fn try_parse_msg<'b>(&mut self) -> Result<Option<Message<'b>>> {
        if self.state == State::TlsNegotiating && self.stream.is_handshake_complete() {
            if self.url.is_http {
                self.state = State::Open;
                return Ok(Some(Message::Open));
            } else {
                self.state = State::Handshaking;
            }
        }
        match self.state {
            State::Handshaking => {
                let mut response = HttpResponse::new();
                match response.parse_from(&mut self.in_buffer) {
                    Ok(()) => {
                        if response.status == Some(StatusCode::SWITCHING_PROTOCOLS) {
                            self.state = State::Open;
                            return Ok(Some(Message::Open));
                        }
                        self.state = State::FinishedClose;
                        logln!("Handshake failed, response: {:?}", response);
                        flush_logs!();
                        Err(Error::new(
                            Kind::WebsocketHandshakeFailed,
                            "Handshake failed.",
                        ))
                    }
                    Err(Error {
                        kind: Kind::IncompleteHttpResponse,
                        details: _,
                    }) => Ok(None),
                    Err(err) => {
                        self.state = State::FinishedClose;
                        Err(Error::new(
                            Kind::WebsocketHandshakeFailed,
                            format!("Handshake failed: {}", err),
                        ))
                    }
                }
            }
            _ => match self.url.is_http {
                false => match self.read_frames() {
                    Ok(Some(msg)) => {
                        match &msg {
                            Message::WebsocketPing(data) => {
                                self.send_pong(data.to_vec())?;
                            }
                            Message::WebsocketPong(_) => {}
                            Message::WebsocketPayload(_) => {
                                return Ok(Some(msg));
                            }
                            _ => {
                                return Err(Error::new(
                                    Kind::Protocol,
                                    "Received unexpected message type.",
                                ));
                            }
                        };
                        Ok(None)
                    }
                    Ok(None) => Ok(None),
                    Err(err) => Err(err),
                },
                true => match self.http_response.parse_from(&mut self.in_buffer) {
                    Ok(()) => {
                        let response = take(&mut self.http_response);
                        Ok(Some(Message::HttpResponse(Cow::Owned(response))))
                    }
                    Err(Error {
                        kind: Kind::IncompleteHttpResponse,
                        details: _,
                    }) => Ok(None),
                    Err(err) => Err(Error::new(
                        Kind::HttpResponseParseFailed,
                        format!("HTTP response failed: {}", err),
                    )),
                },
            },
        }
    }

    fn read_frames<'a>(&mut self) -> Result<Option<Message<'a>>> {
        while let Some(frame) = Frame::parse(&mut self.in_buffer, self.settings.max_fragment_size)?
        {
            if frame.is_final() {
                match frame.opcode() {
                    OpCode::Text => {
                        if !self.fragments.is_empty() {
                            return Err(Error::new(
                                Kind::Protocol,
                                "Received unfragmented text frame while processing fragmented message.",
                            ));
                        }
                        return Ok(Some(Message::from(frame)));
                    }
                    OpCode::Binary => {
                        if !self.fragments.is_empty() {
                            return Err(Error::new(
                                Kind::Protocol,
                                "Received unfragmented binary frame while processing fragmented message.",
                            ));
                        }
                        return Ok(Some(Message::from(frame)));
                    }
                    // control frames
                    OpCode::Close => {
                        if self.state == State::RespondingClose {
                            return Err(Error::new(
                                Kind::Closed,
                                "Received close frame from peer.",
                            ));
                        }

                        self.state = State::RespondingClose;

                        let data = frame.payload;
                        if data.len() >= 2 {
                            let close_code_0 = data[0];
                            let close_code_1 = data[1];
                            let raw_code = (u16::from(close_code_0) << 8) | u16::from(close_code_1);
                            let named = CloseCode::from(raw_code);
                            match named {
                                CloseCode::Empty | CloseCode::Status | CloseCode::Abnormal => {
                                    return Err(Error::new(
                                        Kind::Protocol,
                                        "Received empty close code from endpoint.",
                                    ));
                                }
                                CloseCode::Other(c) => {
                                    if (3000..=4999).contains(&c) {
                                        return Err(Error::new(
                                            Kind::Closing,
                                            "recveived close code from peer",
                                        ));
                                    } else {
                                        return Err(Error::new(Kind::Protocol, ""));
                                    }
                                }
                                _ => {
                                    return Err(Error::new(
                                        Kind::Closing,
                                        format!(
                                            "unkonwn close code {} {:?} {:?}",
                                            raw_code,
                                            named,
                                            String::from_utf8_lossy(&data[2..])
                                        ),
                                    ));
                                }
                            };
                        }
                        return Err(Error::new(Kind::Closing, "Recieve close message from peer"));
                    }
                    OpCode::Ping => {
                        return Ok(Some(Message::WebsocketPing(frame.payload)));
                    }
                    OpCode::Pong => {
                        return Ok(Some(Message::WebsocketPong(frame.payload)));
                    }
                    // last fragment
                    OpCode::Continue => {
                        if let Some(first) = self.fragments.pop_front() {
                            let size =
                                self.fragments.iter().fold(
                                    first.payload.len() + frame.payload.len(),
                                    |len, frame| len + frame.payload.len(),
                                );
                            match first.opcode() {
                                OpCode::Text => {
                                    let mut data = Vec::with_capacity(size);
                                    data.extend(first.payload.into_owned());
                                    while let Some(frame) = self.fragments.pop_front() {
                                        data.extend(frame.payload.into_owned());
                                    }
                                    data.extend(frame.payload.into_owned());
                                    return Ok(Some(Message::WebsocketPayload(Cow::Owned(data))));
                                }
                                OpCode::Binary => {
                                    let mut data = Vec::with_capacity(size);
                                    data.extend(first.payload.into_owned());

                                    while let Some(frame) = self.fragments.pop_front() {
                                        data.extend(frame.payload.into_owned());
                                    }
                                    data.extend(frame.payload.into_owned());
                                    return Ok(Some(Message::WebsocketPayload(Cow::Owned(data))));
                                }
                                _ => {
                                    return Err(Error::new(
                                        Kind::Protocol,
                                        "Encounted fragmented control frame.",
                                    ));
                                }
                            }
                        } else {
                            return Err(Error::new(
                                Kind::Protocol,
                                "Unable to reconstruct fragmented message. No first frame.",
                            ));
                        }
                    }
                    _ => return Err(Error::new(Kind::Protocol, "Encountered invalid opcode.")),
                }
            } else if frame.is_control() {
                return Err(Error::new(
                    Kind::Protocol,
                    "Encounted fragmented control frame.",
                ));
            } else if !self.settings.fragments_grow
                && self.settings.fragments_capacity == self.fragments.len()
            {
                return Err(Error::new(Kind::Capacity, "Exceeded max fragments."));
            } else {
                self.fragments.push_back(frame)
            }
        }
        Ok(None)
    }

    #[inline(always)]
    fn inner_write_message(&mut self, msg: Message) -> Result<()> {
        match msg {
            Message::HttpRequest(mut req) => {
                if self.http_response.is_incomplete() {
                    return Err(Error::new(
                        Kind::HttpRequestInProcess,
                        "Attempted to send HTTP request while another is in process.",
                    ));
                }
                if !self.url.is_http {
                    return Err(Error::new(
                        Kind::Protocol,
                        "Attempted to send HTTP request on non-HTTP connection.",
                    ));
                }
                let mut has_host = false;
                for (key, _) in req.headers.iter() {
                    if key == "Host" {
                        has_host = true;
                        continue;
                    }
                }
                if !has_host {
                    let host = self.url.host.clone();
                    req.headers.add("Host", host);
                }
                req.format(&mut self.out_buffer)?;
            }
            Message::HttpResponse(_)
            | Message::HttpClose
            | Message::HandshakeRequest(_)
            | Message::HandshakeResponse(_) => {
                unreachable!("Attempted to send HTTP response on send_message.")
            }
            _ => {
                let opcode = msg.opcode();
                let frame = match &msg {
                    Message::WebsocketClose(code, reason) => {
                        // change state to awaiting close when sending close frame
                        if self.state != State::RespondingClose {
                            self.state = State::RespondingClose;
                        }
                        Frame::close(*code, reason.as_str())
                    }
                    _ => Frame::message(msg.as_frame_payload(), opcode, true),
                };
                if frame.payload.len() > self.settings.fragment_size {
                    // note this copies the data, so it's actually somewhat expensive to fragment
                    let mut chunks = frame.payload.chunks(self.settings.fragment_size).peekable();
                    let chunk = chunks.next().expect("Unable to get initial chunk!");

                    let mut first = Frame::message(Vec::from(chunk), opcode, false);

                    // Match reserved bits from original to keep extension status intact
                    first.set_rsv1(frame.has_rsv1());
                    first.set_rsv2(frame.has_rsv2());
                    first.set_rsv3(frame.has_rsv3());

                    self.buffer_frame(first)?;

                    while let Some(chunk) = chunks.next() {
                        if chunks.peek().is_some() {
                            self.buffer_frame(Frame::message(
                                Vec::from(chunk),
                                OpCode::Continue,
                                false,
                            ))?;
                        } else {
                            self.buffer_frame(Frame::message(
                                Vec::from(chunk),
                                OpCode::Continue,
                                true,
                            ))?;
                        }
                    }
                } else {
                    // true means that the message is done
                    self.buffer_frame(frame)?;
                }
            }
        }
        Ok(())
    }

    pub fn write_message(&mut self, msg: Message) -> Result<bool> {
        self.inner_write_message(msg)?;
        self.write()
    }

    pub fn send_messages<M>(&mut self, msgs: Vec<M>) -> Result<bool>
    where
        M: Into<Message<'static>>,
    {
        for msg in msgs.into_iter() {
            self.inner_write_message(msg.into())?;
        }
        self.write()
    }

    pub fn write_message_n<const NUM: usize, M>(&mut self, msg: [M; NUM]) -> Result<bool>
    where
        M: Into<Message<'static>>,
    {
        for msg in msg.into_iter() {
            self.inner_write_message(msg.into())?;
        }
        self.write()
    }

    pub fn write(&mut self) -> Result<bool> {
        if self.state == State::FinishedClose {
            return Ok(true);
        }
        self.stream.write_buf(&mut self.out_buffer)
    }

    #[inline]
    pub fn write_buf_mut(&mut self) -> &mut CircularBuffer<OUT_LEN> {
        &mut self.out_buffer
    }

    #[inline]
    pub fn send_pong(&mut self, data: Vec<u8>) -> Result<()> {
        if self.state.is_closing() {
            return Ok(());
        }
        self.buffer_frame(Frame::pong(data))?;
        match self.write() {
            Ok(true) => Ok(()),
            Ok(false) => {
                logln!("write pong failed: {}", self.url);
                flush_logs!();
                Ok(())
            }
            Err(err) => Err(err),
        }
    }

    #[inline(always)]
    fn buffer_frame(&mut self, mut frame: Frame) -> Result<()> {
        frame.set_mask();
        frame.format(&mut self.out_buffer)?;
        Ok(())
    }
}
